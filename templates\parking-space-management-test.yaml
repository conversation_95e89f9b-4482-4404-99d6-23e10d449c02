config:
  name: "停车位管理自动化测试"
  description: "停车位管理的完整CRUD操作测试"
  baseUrl: "http://************:9999"
  executorType: "element-plus"
  testMode: "flow"
  forceCleanupOnFailure: tr
  timeout: 30000
  variables:
    testData:
      parkingSpace1:
        name: "测试停车位AUTO001"
        code: "TEST_AUTO_001"
        chargingPileIndex: 0
        longitude: "116.123456"
        latitude: "39.654321"
        altitude: "50"
        orientation: "北"
      parkingSpace2:
        name: "测试停车位AUTO002"
        code: "TEST_AUTO_002"
        chargingPileIndex: 0
        longitude: "116.789012"
        latitude: "39.987654"
        altitude: "60"
        orientation: "南"
      editData:
        name: "测试停车位AUTO002_编辑"
        longitude: "116.999999"
        latitude: "39.888888"
        altitude: "80"
        orientation: "东"

templates:
  add-parking-space-template:
    name: "添加停车位模板"
    parameters:
      - name: name
        type: "string"
        required: true
      - name: code
        type: "string"
        required: true
      - name: chargingPileIndex
        type: "number"
        required: false
      - name: longitude
        type: "string"
        required: false
      - name: latitude
        type: "string"
        required: false
      - name: altitude
        type: "string"
        required: false
      - name: orientation
        type: "string"
        required: false
    steps:
      - action: click
        role: button
        roleOptions:
          name: "添加"
      - action: fill
        role: textbox
        roleOptions:
          name: "停车位名称"
        within:
          role: dialog
          roleOptions:
            name: "新增停车位"
        data: "{{name}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "停车位编码"
        within:
          role: dialog
          roleOptions:
            name: "新增停车位"
        data: "{{code}}"
      - action: selectOption
        role: combobox
        roleOptions:
          name: "对应充电桩"
        within:
          role: dialog
          roleOptions:
            name: "新增停车位"
        index: "{{chargingPileIndex}}"
        when: "{{chargingPileIndex}} !== undefined"
      - action: fill
        role: textbox
        roleOptions:
          name: "经度"
        within:
          role: dialog
          roleOptions:
            name: "新增停车位"
        data: "{{longitude}}"
        when: "{{longitude}} !== undefined"
      - action: fill
        role: textbox
        roleOptions:
          name: "纬度"
        within:
          role: dialog
          roleOptions:
            name: "新增停车位"
        data: "{{latitude}}"
        when: "{{latitude}} !== undefined"
      - action: fill
        role: textbox
        roleOptions:
          name: "海拔"
        within:
          role: dialog
          roleOptions:
            name: "新增停车位"
        data: "{{altitude}}"
        when: "{{altitude}} !== undefined"
      - action: fill
        role: textbox
        roleOptions:
          name: "朝向"
        within:
          role: dialog
          roleOptions:
            name: "新增停车位"
        data: "{{orientation}}"
        when: "{{orientation}} !== undefined"
      - action: useScript
        script: "dialog-operations"
        parameters:
          action: "confirmDialog"

  edit-parking-space-template:
    name: "编辑停车位模板"
    parameters:
      - name: searchFields
        type: "array"
        required: true
      - name: name
        type: "string"
        required: false
      - name: chargingPileIndex
        type: "number"
        required: false
      - name: longitude
        type: "string"
        required: false
      - name: latitude
        type: "string"
        required: false
      - name: altitude
        type: "string"
        required: false
      - name: orientation
        type: "string"
        required: false
    steps:
      - action: useScript
        script: "table-operations"
        parameters:
          action: "editRow"
          fields: "{{searchFields}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "停车位名称"
        within:
          role: dialog
          roleOptions:
            name: "修改停车位"
        data: "{{name}}"
        when: "{{name}} !== undefined"
      - action: selectOption
        role: combobox
        roleOptions:
          name: "对应充电桩"
        within:
          role: dialog
          roleOptions:
            name: "修改停车位"
        index: "{{chargingPileIndex}}"
        when: "{{chargingPileIndex}} !== undefined"
      - action: fill
        role: textbox
        roleOptions:
          name: "经度"
        within:
          role: dialog
          roleOptions:
            name: "修改停车位"
        data: "{{longitude}}"
        when: "{{longitude}} !== undefined"
      - action: fill
        role: textbox
        roleOptions:
          name: "纬度"
        within:
          role: dialog
          roleOptions:
            name: "修改停车位"
        data: "{{latitude}}"
        when: "{{latitude}} !== undefined"
      - action: fill
        role: textbox
        roleOptions:
          name: "海拔"
        within:
          role: dialog
          roleOptions:
            name: "修改停车位"
        data: "{{altitude}}"
        when: "{{altitude}} !== undefined"
      - action: fill
        role: textbox
        roleOptions:
          name: "朝向"
        within:
          role: dialog
          roleOptions:
            name: "修改停车位"
        data: "{{orientation}}"
        when: "{{orientation}} !== undefined"
      - action: useScript
        script: "dialog-operations"
        parameters:
          action: "confirmDialog"

  delete-parking-space-template:
    name: "删除停车位模板"
    parameters:
      - name: searchFields
        type: "array"
        required: true
    steps:
      - action: useScript
        script: "table-operations"
        parameters:
          action: "deleteRow"
          fields: "{{searchFields}}"

  search-parking-space-template:
    name: "查询停车位模板"
    parameters:
      - name: searchText
        type: "string"
        required: false
    steps:
      - action: fill
        role: textbox
        roleOptions:
          name: "停车位名称/编码"
        data: "{{searchText}}"
        when: "{{searchText}} !== undefined"
      - action: click
        role: button
        roleOptions:
          name: "查询"
      - action: wait
        data: 2000
      - action: click
        role: button
        roleOptions:
          name: "重置"

tests:
  - name: "停车位管理完整流程测试"
    description: "测试停车位的添加、查询、编辑、删除功能"
    steps:
      # 第一步：添加第一个停车位
      - action: useTemplate
        template: "add-parking-space-template"
        parameters:
          name: "{{testData.parkingSpace1.name}}"
          code: "{{testData.parkingSpace1.code}}"
          chargingPileIndex: "{{testData.parkingSpace1.chargingPileIndex}}"
          longitude: "{{testData.parkingSpace1.longitude}}"
          latitude: "{{testData.parkingSpace1.latitude}}"
          altitude: "{{testData.parkingSpace1.altitude}}"
          orientation: "{{testData.parkingSpace1.orientation}}"
      
      # 第二步：添加第二个停车位
      - action: useTemplate
        template: "add-parking-space-template"
        parameters:
          name: "{{testData.parkingSpace2.name}}"
          code: "{{testData.parkingSpace2.code}}"
          chargingPileIndex: "{{testData.parkingSpace2.chargingPileIndex}}"
          longitude: "{{testData.parkingSpace2.longitude}}"
          latitude: "{{testData.parkingSpace2.latitude}}"
          altitude: "{{testData.parkingSpace2.altitude}}"
          orientation: "{{testData.parkingSpace2.orientation}}"
      
      # 第三步：查询功能测试
      - action: useTemplate
        template: "search-parking-space-template"
        parameters:
          searchText: "{{testData.parkingSpace1.name}}"
      
      # 第四步：编辑第二个停车位
      - action: useTemplate
        template: "edit-parking-space-template"
        parameters:
          searchFields:
            - column: "名称"
              value: "{{testData.parkingSpace2.name}}"
              exact: true
          name: "{{testData.editData.name}}"
          longitude: "{{testData.editData.longitude}}"
          latitude: "{{testData.editData.latitude}}"
          altitude: "{{testData.editData.altitude}}"
          orientation: "{{testData.editData.orientation}}"
      
      # 第五步：验证编辑结果
      - action: useTemplate
        template: "search-parking-space-template"
        parameters:
          searchText: "{{testData.editData.name}}"

afterAll:
  - name: "清理测试数据"
    description: "删除测试过程中创建的数据"
    steps:
      - action: reload
      # 删除第一个停车位
      - action: useTemplate
        template: "delete-parking-space-template"
        parameters:
          searchFields:
            - column: "名称"
              value: "{{testData.parkingSpace1.name}}"
              exact: true
      
      # 删除编辑后的第二个停车位
      - action: useTemplate
        template: "delete-parking-space-template"
        parameters:
          searchFields:
            - column: "名称"
              value: "{{testData.editData.name}}"
              exact: true